<div class="app-container">
  <app-sidebar (sidebarToggle)="onSidebarToggle($event)" #sidebar></app-sidebar>

  <!-- Mobile toggle button (shown when sidebar is collapsed on mobile) -->
  <button class="mobile-toggle"
          *ngIf="!sidebarExpanded"
          (click)="sidebar.toggleSidebar()"
          [attr.aria-label]="'Open sidebar'">
    <span class="hamburger-icon">
      <span></span>
      <span></span>
      <span></span>
    </span>
  </button>

  <main class="main-content" [class.sidebar-expanded]="sidebarExpanded">
    <router-outlet></router-outlet>
  </main>
</div>